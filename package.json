{"name": "aibanative", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"@react-native-community/async-storage": "^1.12.1", "@react-native-community/datetimepicker": "^3.5.2", "@react-native-community/netinfo": "^9.3.0", "@react-native-community/push-notification-ios": "^1.10.1", "@react-native-community/viewpager": "^5.0.11", "@react-native-picker/picker": "^2.1.0", "@react-navigation/native": "^6.0.2", "@react-navigation/native-stack": "^6.0.4", "@supersami/rn-foreground-service": "^1.1.1", "axios": "^0.21.1", "date-fns": "^2.27.0", "git-filter-repo": "^0.0.21", "immer": "^9.0.5", "moment": "^2.29.1", "react": "17.0.1", "react-geocode": "^0.2.3", "react-native": "^0.66.1", "react-native-autocomplete-dropdown": "^1.1.4", "react-native-camera": "^4.1.1", "react-native-date-picker": "^4.1.0", "react-native-elements": "^3.4.2", "react-native-geolocation-service": "^5.3.0-beta.1", "react-native-image-picker": "^4.1.2", "react-native-pager-view": "^5.4.6", "react-native-permissions": "^3.3.1", "react-native-progress": "^5.0.0", "react-native-push-notification": "^8.1.1", "react-native-qrcode-scanner": "^1.5.4", "react-native-qrcode-svg": "^6.1.1", "react-native-safe-area-context": "^3.3.0", "react-native-screens": "^3.5.0", "react-native-svg": "^12.1.1", "react-native-tab-view": "^3.1.1", "react-native-vector-icons": "^9.0.0", "react-native-webview": "^11.17.0", "rn-modal-picker": "^0.3.2", "socket.io-client": "4.2.0", "use-immer": "^0.6.0"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/runtime": "^7.12.5", "@react-native-community/eslint-config": "^2.0.0", "@testing-library/react-native": "^8.0.0", "babel-jest": "^26.6.3", "eslint": "7.14.0", "jest": "^26.6.3", "metro-react-native-babel-preset": "^0.64.0", "react-test-renderer": "17.0.1"}, "jest": {"preset": "react-native"}}